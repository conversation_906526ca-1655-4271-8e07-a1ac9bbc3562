package com.k2fsa.sherpa.onnx

import android.os.Bundle
import android.text.InputType
import android.util.Log
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.*

/**
 * LLM设置页面
 * 用于配置LLM提供商、API密钥和自动优化设置
 */
class SettingsActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "SettingsActivity"
    }

    // UI组件
    private lateinit var rgLlmProvider: RadioGroup
    private lateinit var rbGemini: RadioButton
    private lateinit var rbDeepSeek: RadioButton
    private lateinit var etGeminiApiKey: EditText
    private lateinit var etDeepSeekApiKey: EditText
    private lateinit var btnToggleGeminiVisibility: Button
    private lateinit var btnToggleDeepSeekVisibility: Button
    private lateinit var btnClearGemini: Button
    private lateinit var btnClearDeepSeek: Button
    private lateinit var switchAutoOptimize: Switch
    private lateinit var btnSaveSettings: Button
    private lateinit var btnTestConnection: Button
    private lateinit var tvStatus: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        // 设置标题栏
        supportActionBar?.title = "LLM 设置"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        initViews()
        loadCurrentSettings()
        setupEventListeners()
        updateStatus()
    }

    private fun initViews() {
        rgLlmProvider = findViewById(R.id.rg_llm_provider)
        rbGemini = findViewById(R.id.rb_gemini)
        rbDeepSeek = findViewById(R.id.rb_deepseek)
        etGeminiApiKey = findViewById(R.id.et_gemini_api_key)
        etDeepSeekApiKey = findViewById(R.id.et_deepseek_api_key)
        btnToggleGeminiVisibility = findViewById(R.id.btn_toggle_gemini_visibility)
        btnToggleDeepSeekVisibility = findViewById(R.id.btn_toggle_deepseek_visibility)
        btnClearGemini = findViewById(R.id.btn_clear_gemini)
        btnClearDeepSeek = findViewById(R.id.btn_clear_deepseek)
        switchAutoOptimize = findViewById(R.id.switch_auto_optimize)
        btnSaveSettings = findViewById(R.id.btn_save_settings)
        btnTestConnection = findViewById(R.id.btn_test_connection)
        tvStatus = findViewById(R.id.tv_status)
    }

    private fun loadCurrentSettings() {
        try {
            // 加载当前选择的提供商
            val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
            when (currentProvider) {
                LLMProvider.GEMINI -> rbGemini.isChecked = true
                LLMProvider.DEEPSEEK -> rbDeepSeek.isChecked = true
            }

            // 加载API密钥（显示为掩码）
            val geminiKey = LLMApiKeyManager.getApiKey(this, LLMProvider.GEMINI)
            if (geminiKey.isNotBlank()) {
                etGeminiApiKey.setText(maskApiKey(geminiKey))
            }

            val deepSeekKey = LLMApiKeyManager.getApiKey(this, LLMProvider.DEEPSEEK)
            if (deepSeekKey.isNotBlank()) {
                etDeepSeekApiKey.setText(maskApiKey(deepSeekKey))
            }

            // 加载自动优化设置
            switchAutoOptimize.isChecked = LLMApiKeyManager.getAutoOptimize(this)

            Log.d(TAG, "设置加载完成")
        } catch (e: Exception) {
            Log.e(TAG, "加载设置失败", e)
            showToast("加载设置失败: ${e.message}")
        }
    }

    private fun setupEventListeners() {
        // 密钥可见性切换
        btnToggleGeminiVisibility.setOnClickListener {
            togglePasswordVisibility(etGeminiApiKey)
        }

        btnToggleDeepSeekVisibility.setOnClickListener {
            togglePasswordVisibility(etDeepSeekApiKey)
        }

        // 清除密钥
        btnClearGemini.setOnClickListener {
            etGeminiApiKey.setText("")
            showToast("Gemini API密钥已清除")
        }

        btnClearDeepSeek.setOnClickListener {
            etDeepSeekApiKey.setText("")
            showToast("DeepSeek API密钥已清除")
        }

        // 保存设置
        btnSaveSettings.setOnClickListener {
            saveSettings()
        }

        // 测试连接
        btnTestConnection.setOnClickListener {
            testConnection()
        }

        // 提供商选择变化
        rgLlmProvider.setOnCheckedChangeListener { _, _ ->
            updateStatus()
        }
    }

    private fun saveSettings() {
        try {
            // 保存选择的提供商
            val selectedProvider = when (rgLlmProvider.checkedRadioButtonId) {
                R.id.rb_gemini -> LLMProvider.GEMINI
                R.id.rb_deepseek -> LLMProvider.DEEPSEEK
                else -> LLMProvider.GEMINI
            }
            LLMApiKeyManager.setCurrentProvider(this, selectedProvider)

            // 保存API密钥（只有在不是掩码时才保存）
            val geminiKey = etGeminiApiKey.text.toString().trim()
            if (geminiKey.isNotBlank() && !geminiKey.contains("*")) {
                LLMApiKeyManager.saveApiKey(this, LLMProvider.GEMINI, geminiKey)
            }

            val deepSeekKey = etDeepSeekApiKey.text.toString().trim()
            if (deepSeekKey.isNotBlank() && !deepSeekKey.contains("*")) {
                LLMApiKeyManager.saveApiKey(this, LLMProvider.DEEPSEEK, deepSeekKey)
            }

            // 保存自动优化设置
            LLMApiKeyManager.setAutoOptimize(this, switchAutoOptimize.isChecked)

            updateStatus()
            showToast("✅ 设置已保存")
            Log.d(TAG, "设置保存成功")

        } catch (e: Exception) {
            Log.e(TAG, "保存设置失败", e)
            showToast("❌ 保存设置失败: ${e.message}")
        }
    }

    private fun testConnection() {
        val selectedProvider = when (rgLlmProvider.checkedRadioButtonId) {
            R.id.rb_gemini -> LLMProvider.GEMINI
            R.id.rb_deepseek -> LLMProvider.DEEPSEEK
            else -> LLMProvider.GEMINI
        }

        if (!LLMManager.isLLMAvailable(this, selectedProvider)) {
            showToast("❌ 请先配置 ${selectedProvider.displayName} 的API密钥")
            return
        }

        tvStatus.text = "🔄 正在测试 ${selectedProvider.displayName} 连接..."
        btnTestConnection.isEnabled = false

        // 在后台线程测试连接
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.callLLMAPI(
                    this@SettingsActivity,
                    selectedProvider,
                    "请回复'连接测试成功'"
                )

                runOnUiThread {
                    btnTestConnection.isEnabled = true
                    if (result.success) {
                        tvStatus.text = "✅ ${selectedProvider.displayName} 连接测试成功"
                        showToast("✅ 连接测试成功")
                    } else {
                        tvStatus.text = "❌ ${selectedProvider.displayName} 连接测试失败"
                        showToast("❌ 连接测试失败: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    btnTestConnection.isEnabled = true
                    tvStatus.text = "❌ ${selectedProvider.displayName} 连接测试异常"
                    showToast("❌ 连接测试异常: ${e.message}")
                }
            }
        }
    }

    private fun updateStatus() {
        val selectedProvider = when (rgLlmProvider.checkedRadioButtonId) {
            R.id.rb_gemini -> LLMProvider.GEMINI
            R.id.rb_deepseek -> LLMProvider.DEEPSEEK
            else -> LLMProvider.GEMINI
        }

        val isAvailable = LLMManager.isLLMAvailable(this, selectedProvider)
        val autoOptimize = switchAutoOptimize.isChecked

        val statusText = buildString {
            append("当前选择: ${selectedProvider.displayName}")
            append(" | 状态: ${if (isAvailable) "✅ 可用" else "❌ 未配置"}")
            append(" | 自动优化: ${if (autoOptimize) "✅ 开启" else "❌ 关闭"}")
        }

        tvStatus.text = statusText
    }

    private fun togglePasswordVisibility(editText: EditText) {
        if (editText.inputType == InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD) {
            editText.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
        } else {
            editText.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
        }
        editText.setSelection(editText.text.length)
    }

    private fun maskApiKey(apiKey: String): String {
        return if (apiKey.length > 8) {
            apiKey.substring(0, 4) + "*".repeat(apiKey.length - 8) + apiKey.substring(apiKey.length - 4)
        } else {
            "*".repeat(apiKey.length)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
