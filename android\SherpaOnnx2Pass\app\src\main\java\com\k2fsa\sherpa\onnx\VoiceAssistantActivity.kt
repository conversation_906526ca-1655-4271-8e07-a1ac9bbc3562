package com.k2fsa.sherpa.onnx

import android.Manifest
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.drawable.AnimatedVectorDrawable
import android.media.AudioRecord
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Apple-inspired Voice Assistant Activity
 * 实现简洁优雅的语音识别体验
 */
class VoiceAssistantActivity : AppCompatActivity(), SingleModelASREngine.ASRListener {
    
    companion object {
        private const val TAG = "VoiceAssistant"
        private const val PERMISSION_REQUEST_CODE = 1001
        private const val SAMPLE_RATE = 16000
    }
    
    // UI Components
    private lateinit var tvStatus: TextView
    private lateinit var btnRecord: MaterialButton
    private lateinit var tvRecordingStatus: TextView
    private lateinit var ivPulseAnimation: ImageView
    private lateinit var llWaveform: LinearLayout
    private lateinit var cardPreview: CardView
    private lateinit var tvPreview: TextView
    private lateinit var cardResults: CardView
    private lateinit var tvResults: TextView
    private lateinit var tvWordCount: TextView
    private lateinit var llActions: LinearLayout
    private lateinit var btnSummary: MaterialButton
    private lateinit var btnClear: MaterialButton
    private lateinit var btnSettings: ImageButton
    
    // Core Components
    private lateinit var asrEngine: SingleModelASREngine
    private var audioRecord: AudioRecord? = null
    private val isRecording = AtomicBoolean(false)
    private val isInitialized = AtomicBoolean(false)
    
    // Data
    private val recognitionResults = StringBuilder()
    private var wordCount = 0
    private val mainHandler = Handler(Looper.getMainLooper())

    // AI Optimization Settings
    private var autoOptimize = true
    
    // Animation
    private var pulseAnimator: AnimatorSet? = null
    private var waveformAnimator: ValueAnimator? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_voice_assistant)
        
        initializeUI()
        loadSettings()
        checkPermissions()
    }
    
    private fun initializeUI() {
        try {
            // Find views
            tvStatus = findViewById(R.id.tv_status)
            btnRecord = findViewById(R.id.btn_record)
            tvRecordingStatus = findViewById(R.id.tv_recording_status)
            ivPulseAnimation = findViewById(R.id.iv_pulse_animation)
            llWaveform = findViewById(R.id.ll_waveform)
            cardPreview = findViewById(R.id.card_preview)
            tvPreview = findViewById(R.id.tv_preview)
            cardResults = findViewById(R.id.card_results)
            tvResults = findViewById(R.id.tv_results)
            tvWordCount = findViewById(R.id.tv_word_count)
            llActions = findViewById(R.id.ll_actions)
            btnSummary = findViewById(R.id.btn_summary)
            btnClear = findViewById(R.id.btn_clear)
            btnSettings = findViewById(R.id.btn_settings)
            
            // Set click listeners
            btnRecord.setOnClickListener { toggleRecording() }
            btnSummary.setOnClickListener { generateSummary() }
            btnClear.setOnClickListener { clearResults() }
            btnSettings.setOnClickListener { openSettings() }
            
            // Set long click listeners for advanced features
            tvResults.setOnLongClickListener { 
                showOptimizationMenu()
                true
            }
            
            // Initialize UI state
            updateUIState(UIState.INITIALIZING)
            
            Log.i(TAG, "UI initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize UI", e)
            showToast("UI初始化失败: ${e.message}")
        }
    }

    private fun loadSettings() {
        try {
            // 从LLMApiKeyManager加载自动优化设置
            autoOptimize = LLMApiKeyManager.getAutoOptimize(this)
            Log.d(TAG, "自动优化设置已加载: $autoOptimize")
        } catch (e: Exception) {
            Log.e(TAG, "加载设置失败", e)
            // 使用默认值
            autoOptimize = true
        }
    }

    private fun checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) 
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                this, 
                arrayOf(Manifest.permission.RECORD_AUDIO), 
                PERMISSION_REQUEST_CODE
            )
        } else {
            initializeASREngine()
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int, 
        permissions: Array<out String>, 
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                initializeASREngine()
            } else {
                showToast("需要麦克风权限才能使用语音识别功能")
                updateUIState(UIState.ERROR)
            }
        }
    }
    
    private fun initializeASREngine() {
        updateUIState(UIState.INITIALIZING)
        
        Thread {
            try {
                asrEngine = SingleModelASREngine(assets, SAMPLE_RATE, context = this)
                asrEngine.setListener(this)
                
                val success = asrEngine.initialize()
                
                runOnUiThread {
                    if (success) {
                        isInitialized.set(true)
                        updateUIState(UIState.READY)
                        
                        val speakerCount = asrEngine.getSpeakerCount()
                        if (speakerCount > 0) {
                            showToast("已恢复 $speakerCount 个声纹")
                        }
                    } else {
                        updateUIState(UIState.ERROR)
                        showToast("初始化失败")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    updateUIState(UIState.ERROR)
                    showToast("初始化异常: ${e.message}")
                }
                Log.e(TAG, "ASR engine initialization failed", e)
            }
        }.start()
    }
    
    private fun toggleRecording() {
        if (!isInitialized.get()) {
            showToast("请等待初始化完成")
            return
        }
        
        if (isRecording.get()) {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private fun startRecording() {
        try {
            // Reset ASR engine state before starting new recording
            asrEngine.reset()

            isRecording.set(true)
            updateUIState(UIState.RECORDING)
            startRecordingAnimations()

            // Start audio recording thread
            startAudioRecording()

            Log.i(TAG, "Recording started")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start recording", e)
            showToast("录音启动异常: ${e.message}")
        }
    }

    private fun stopRecording() {
        try {
            isRecording.set(false)
            updateUIState(UIState.PROCESSING)
            stopRecordingAnimations()

            // Stop audio recording
            stopAudioRecording()

            // 自动优化ASR结果
            if (autoOptimize && recognitionResults.isNotEmpty()) {
                autoOptimizeAsrContent()
            }

            // Show actions if we have results
            if (recognitionResults.isNotEmpty()) {
                llActions.visibility = View.VISIBLE
                animateViewIn(llActions)
            }

            Log.i(TAG, "Recording stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop recording", e)
            showToast("录音停止异常: ${e.message}")
        }
    }
    
    // ASRListener implementation
    override fun onResult(result: SingleModelASREngine.ASRResult) {
        runOnUiThread {
            when (result.type) {
                SingleModelASREngine.ResultType.PREVIEW -> {
                    tvPreview.text = result.text
                    if (cardPreview.visibility != View.VISIBLE) {
                        cardPreview.visibility = View.VISIBLE
                        animateViewIn(cardPreview)
                    }
                }
                SingleModelASREngine.ResultType.FINAL, SingleModelASREngine.ResultType.ENDPOINT -> {
                    if (result.text.isNotBlank()) {
                        val speakerName = if (result.hasSpeakerInfo) result.speakerName else "说话人"
                        recognitionResults.append("$speakerName: ${result.text}\n")
                        tvResults.text = recognitionResults.toString()
                        updateWordCount()
                    }
                }
            }
        }
    }

    override fun onError(error: String) {
        runOnUiThread {
            Log.e(TAG, "ASR Error: $error")
            showToast("识别错误: $error")
            if (isRecording.get()) {
                stopRecording()
            }
        }
    }

    override fun onStatusChanged(status: String) {
        runOnUiThread {
            Log.d(TAG, "Status: $status")
        }
    }

    override fun onSpeakerIdentified(speakerInfo: SingleModelASREngine.SpeakerInfo) {
        runOnUiThread {
            Log.d(TAG, "Speaker identified: ${speakerInfo.name}")
        }
    }

    override fun onSpeakerRegistered(speakerName: String, success: Boolean) {
        runOnUiThread {
            Log.d(TAG, "Speaker registered: $speakerName, success: $success")
        }
    }

    override fun onSpeakerRemoved(speakerName: String, success: Boolean) {
        runOnUiThread {
            Log.d(TAG, "Speaker removed: $speakerName, success: $success")
        }
    }

    override fun onVadStatusChanged(isSpeech: Boolean) {
        runOnUiThread {
            Log.d(TAG, "VAD status changed: $isSpeech")
        }
    }
    
    private fun updateUIState(state: UIState) {
        when (state) {
            UIState.INITIALIZING -> {
                tvStatus.text = getString(R.string.status_initializing)
                tvStatus.setTextColor(ContextCompat.getColor(this, R.color.apple_orange))
                tvRecordingStatus.text = "正在准备..."
                btnRecord.isEnabled = false
            }
            UIState.READY -> {
                tvStatus.text = getString(R.string.status_ready)
                tvStatus.setTextColor(ContextCompat.getColor(this, R.color.apple_green))
                tvRecordingStatus.text = getString(R.string.recording_tap_to_start)
                btnRecord.isEnabled = true
                btnRecord.setBackgroundResource(R.drawable.record_button_idle)
            }
            UIState.RECORDING -> {
                tvStatus.text = getString(R.string.status_recording)
                tvStatus.setTextColor(ContextCompat.getColor(this, R.color.apple_red))
                tvRecordingStatus.text = getString(R.string.recording_tap_to_stop)
                btnRecord.setBackgroundResource(R.drawable.record_button_recording)
            }
            UIState.PROCESSING -> {
                tvStatus.text = getString(R.string.status_processing)
                tvStatus.setTextColor(ContextCompat.getColor(this, R.color.apple_orange))
                tvRecordingStatus.text = getString(R.string.recording_tap_to_start)
                btnRecord.setBackgroundResource(R.drawable.record_button_idle)
                
                // Auto-transition to ready after processing
                mainHandler.postDelayed({
                    updateUIState(UIState.READY)
                }, 2000)
            }
            UIState.ERROR -> {
                tvStatus.text = getString(R.string.status_error)
                tvStatus.setTextColor(ContextCompat.getColor(this, R.color.apple_red))
                tvRecordingStatus.text = "请检查设置"
                btnRecord.isEnabled = false
            }
        }
    }
    
    private fun startRecordingAnimations() {
        // Show pulse animation
        ivPulseAnimation.visibility = View.VISIBLE
        val pulseDrawable = ivPulseAnimation.drawable as? AnimatedVectorDrawable
        pulseDrawable?.start()
        
        // Show waveform
        llWaveform.visibility = View.VISIBLE
        animateViewIn(llWaveform)
        
        // Start waveform animation
        startWaveformAnimation()
    }
    
    private fun stopRecordingAnimations() {
        // Hide pulse animation
        ivPulseAnimation.visibility = View.GONE
        
        // Hide waveform
        animateViewOut(llWaveform)
        
        // Stop waveform animation
        waveformAnimator?.cancel()
    }
    
    private fun startWaveformAnimation() {
        // Create animated waveform bars
        llWaveform.removeAllViews()
        val barCount = 20
        val bars = mutableListOf<View>()
        
        for (i in 0 until barCount) {
            val bar = View(this).apply {
                layoutParams = LinearLayout.LayoutParams(8, 20).apply {
                    marginStart = 4
                    marginEnd = 4
                }
                setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.waveform_active))
            }
            llWaveform.addView(bar)
            bars.add(bar)
        }
        
        // Animate bars
        waveformAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = 100
            repeatCount = ValueAnimator.INFINITE
            addUpdateListener { animator ->
                bars.forEachIndexed { index, bar ->
                    val height = (20 + Math.random() * 40).toInt()
                    bar.layoutParams.height = height
                    bar.requestLayout()
                }
            }
        }
        waveformAnimator?.start()
    }
    
    private fun animateViewIn(view: View) {
        view.alpha = 0f
        view.scaleX = 0.8f
        view.scaleY = 0.8f
        view.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(300)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()
    }
    
    private fun animateViewOut(view: View) {
        view.animate()
            .alpha(0f)
            .scaleX(0.8f)
            .scaleY(0.8f)
            .setDuration(200)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withEndAction {
                view.visibility = View.GONE
                view.alpha = 1f
                view.scaleX = 1f
                view.scaleY = 1f
            }
            .start()
    }
    
    private fun updateWordCount() {
        wordCount = recognitionResults.toString().length
        tvWordCount.text = getString(R.string.word_count_format, wordCount)
    }
    
    private fun generateSummary() {
        if (recognitionResults.isEmpty()) {
            showToast("没有内容可以总结")
            return
        }
        
        // Use existing LLM integration
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            openSettings()
            return
        }
        
        showToast("正在生成智能总结...")
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.generateMeetingSummary(this@VoiceAssistantActivity, recognitionResults.toString())
                runOnUiThread {
                    if (result.success) {
                        showSummaryDialog(result.content)
                    } else {
                        showToast("总结生成失败: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    showToast("总结生成异常: ${e.message}")
                }
            }
        }
    }
    
    private fun showSummaryDialog(summary: String) {
        val dialog = androidx.appcompat.app.AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .create()

        // 创建苹果风格的自定义布局
        val scrollView = androidx.core.widget.NestedScrollView(this).apply {
            setPadding(24, 24, 24, 24)
        }

        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
        }

        // 标题
        val titleText = TextView(this).apply {
            text = "🤖 AI智能总结"
            textSize = 20f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 24)
            gravity = android.view.Gravity.CENTER
        }

        // 总结内容卡片
        val summaryCard = androidx.cardview.widget.CardView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                bottomMargin = 24
            }
            setCardBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_system_background))
            radius = 16f
            cardElevation = 2f
        }

        val summaryText = TextView(this).apply {
            text = summary
            textSize = 16f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            setPadding(20, 20, 20, 20)
            setLineSpacing(6f, 1.0f)
            setTextIsSelectable(true)
        }

        summaryCard.addView(summaryText)

        // 按钮区域
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER
        }

        val copyBtn = com.google.android.material.button.MaterialButton(this).apply {
            text = "复制总结"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 12, 0)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_blue))
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.white))
            cornerRadius = 12
        }

        val closeBtn = com.google.android.material.button.MaterialButton(this).apply {
            text = "关闭"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(12, 0, 0, 0)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_gray_5))
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            cornerRadius = 12
        }

        buttonLayout.addView(copyBtn)
        buttonLayout.addView(closeBtn)

        layout.addView(titleText)
        layout.addView(summaryCard)
        layout.addView(buttonLayout)

        scrollView.addView(layout)
        dialog.setView(scrollView)

        // 设置按钮点击事件
        copyBtn.setOnClickListener {
            copyToClipboard("AI智能总结", summary)
            showToast("总结已复制到剪贴板")
        }

        closeBtn.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 自动优化ASR内容 - 停止录音后自动执行
     */
    private fun autoOptimizeAsrContent() {
        // 检查当前LLM是否可用，如果没有配置则静默跳过
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            Log.d(TAG, "当前LLM未配置，跳过自动优化")
            return
        }

        val originalContent = recognitionResults.toString()
        if (originalContent.trim().isEmpty()) {
            Log.d(TAG, "识别结果为空，跳过自动优化")
            return
        }

        // 显示优化提示
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)
        showToast("🔄 正在使用${currentProvider.displayName}自动优化ASR结果...")
        Log.i(TAG, "开始使用${currentProvider.displayName}自动优化ASR结果")

        // 后台执行优化
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.optimizeAsrContent(this@VoiceAssistantActivity, originalContent)

                runOnUiThread {
                    if (result.success) {
                        // 直接替换结果
                        recognitionResults.clear()
                        recognitionResults.append(result.content)
                        tvResults.text = recognitionResults.toString()

                        // 更新统计
                        updateWordCount()

                        showToast("✅ ASR结果已自动优化完成")
                        Log.i(TAG, "自动优化ASR结果成功")
                    } else {
                        Log.w(TAG, "自动优化失败: ${result.error}")
                        showToast("⚠️ 自动优化失败，保持原始结果")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    Log.w(TAG, "自动优化异常，保持原始结果", e)
                    showToast("⚠️ 自动优化异常，保持原始结果")
                }
            }
        }
    }

    private fun showOptimizationMenu() {
        if (recognitionResults.isEmpty()) {
            showToast("没有语音识别内容可以优化")
            return
        }

        // 检查当前LLM是否可用
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            showToast("请先在设置中配置LLM API密钥")
            openSettings()
            return
        }

        // 获取当前的语音转文字内容
        val originalContent = recognitionResults.toString()

        if (originalContent.trim().isEmpty()) {
            showToast("语音识别内容为空")
            return
        }

        // 获取当前LLM提供商信息
        val currentProvider = LLMApiKeyManager.getCurrentProvider(this)

        // 显示加载对话框
        val loadingDialog = androidx.appcompat.app.AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .setTitle("🔄 ASR 内容优化")
            .setMessage("正在使用 ${currentProvider.displayName} 优化语音识别内容...\n\n" +
                    "优化内容：\n" +
                    "• 修正识别错误\n" +
                    "• 按说话人分段\n" +
                    "• 整合碎片句子\n" +
                    "• 优化标点符号")
            .setCancelable(false)
            .create()
        loadingDialog.show()

        // 在后台线程调用LLM API
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.optimizeAsrContent(this@VoiceAssistantActivity, originalContent)

                runOnUiThread {
                    loadingDialog.dismiss()
                    if (result.success) {
                        showOptimizedContentDialog(result.content, originalContent)
                    } else {
                        Log.e(TAG, "ASR 内容优化失败: ${result.error}")
                        showToast("ASR 内容优化失败: ${result.error}")
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    loadingDialog.dismiss()
                    Log.e(TAG, "ASR 内容优化异常", e)
                    showToast("ASR 内容优化异常: ${e.message}")
                }
            }
        }
    }

    private fun showOptimizedContentDialog(optimizedContent: String, originalContent: String) {
        val dialog = androidx.appcompat.app.AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .create()

        val scrollView = androidx.core.widget.NestedScrollView(this).apply {
            setPadding(24, 24, 24, 24)
        }

        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
        }

        // 标题
        val titleText = TextView(this).apply {
            text = "✨ 优化结果"
            textSize = 20f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 24)
            gravity = android.view.Gravity.CENTER
        }

        // 优化内容卡片
        val optimizedCard = androidx.cardview.widget.CardView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                bottomMargin = 24
            }
            setCardBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_system_background))
            radius = 16f
            cardElevation = 2f
        }

        val optimizedText = TextView(this).apply {
            text = optimizedContent
            textSize = 16f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            setPadding(20, 20, 20, 20)
            setLineSpacing(6f, 1.0f)
            setTextIsSelectable(true)
        }

        optimizedCard.addView(optimizedText)

        // 按钮区域
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER
        }

        val replaceBtn = com.google.android.material.button.MaterialButton(this).apply {
            text = "替换原文"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 8, 0)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_blue))
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.white))
            cornerRadius = 12
        }

        val copyBtn = com.google.android.material.button.MaterialButton(this).apply {
            text = "复制"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(8, 0, 8, 0)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_green))
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.white))
            cornerRadius = 12
        }

        val compareBtn = com.google.android.material.button.MaterialButton(this).apply {
            text = "对比"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(8, 0, 0, 0)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_gray_5))
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            cornerRadius = 12
        }

        buttonLayout.addView(replaceBtn)
        buttonLayout.addView(copyBtn)
        buttonLayout.addView(compareBtn)

        layout.addView(titleText)
        layout.addView(optimizedCard)
        layout.addView(buttonLayout)

        scrollView.addView(layout)
        dialog.setView(scrollView)

        // 设置按钮点击事件
        replaceBtn.setOnClickListener {
            // 替换原始内容
            recognitionResults.clear()
            recognitionResults.append(optimizedContent)
            tvResults.text = recognitionResults.toString()

            // 更新统计
            updateWordCount()

            showToast("已替换为优化后的内容")
            dialog.dismiss()
        }

        copyBtn.setOnClickListener {
            copyToClipboard("ASR优化内容", optimizedContent)
            showToast("优化内容已复制到剪贴板")
        }

        compareBtn.setOnClickListener {
            dialog.dismiss()
            showComparisonDialog(optimizedContent, originalContent)
        }

        dialog.show()
    }

    private fun showComparisonDialog(optimizedContent: String, originalContent: String) {
        val dialog = androidx.appcompat.app.AlertDialog.Builder(this, R.style.Theme_VoiceAssistant)
            .create()

        val scrollView = androidx.core.widget.NestedScrollView(this).apply {
            setPadding(24, 24, 24, 24)
        }

        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
        }

        // 标题
        val titleText = TextView(this).apply {
            text = "📊 内容对比"
            textSize = 20f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 24)
            gravity = android.view.Gravity.CENTER
        }

        // 原始内容标签
        val originalLabel = TextView(this).apply {
            text = "原始内容"
            textSize = 16f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_secondary_label))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 8)
        }

        // 原始内容卡片
        val originalCard = androidx.cardview.widget.CardView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                bottomMargin = 16
            }
            setCardBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_gray_6))
            radius = 12f
            cardElevation = 1f
        }

        val originalText = TextView(this).apply {
            text = originalContent
            textSize = 14f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_secondary_label))
            setPadding(16, 16, 16, 16)
            setLineSpacing(4f, 1.0f)
            setTextIsSelectable(true)
        }

        originalCard.addView(originalText)

        // 分割线
        val divider = View(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                2
            ).apply {
                setMargins(0, 8, 0, 16)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_separator))
        }

        // 优化内容标签
        val optimizedLabel = TextView(this).apply {
            text = "优化后内容"
            textSize = 16f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_blue))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 0, 0, 8)
        }

        // 优化内容卡片
        val optimizedCard = androidx.cardview.widget.CardView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                bottomMargin = 24
            }
            setCardBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_blue_ultra_light))
            radius = 12f
            cardElevation = 1f
        }

        val optimizedText = TextView(this).apply {
            text = optimizedContent
            textSize = 14f
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_blue_dark))
            setPadding(16, 16, 16, 16)
            setLineSpacing(4f, 1.0f)
            setTextIsSelectable(true)
        }

        optimizedCard.addView(optimizedText)

        // 按钮区域
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER
        }

        val replaceBtn = com.google.android.material.button.MaterialButton(this).apply {
            text = "使用优化版本"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(0, 0, 12, 0)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_blue))
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.white))
            cornerRadius = 12
        }

        val closeBtn = com.google.android.material.button.MaterialButton(this).apply {
            text = "关闭"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f).apply {
                setMargins(12, 0, 0, 0)
            }
            setBackgroundColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_gray_5))
            setTextColor(ContextCompat.getColor(this@VoiceAssistantActivity, R.color.apple_label))
            cornerRadius = 12
        }

        buttonLayout.addView(replaceBtn)
        buttonLayout.addView(closeBtn)

        layout.addView(titleText)
        layout.addView(originalLabel)
        layout.addView(originalCard)
        layout.addView(divider)
        layout.addView(optimizedLabel)
        layout.addView(optimizedCard)
        layout.addView(buttonLayout)

        scrollView.addView(layout)
        dialog.setView(scrollView)

        // 设置按钮点击事件
        replaceBtn.setOnClickListener {
            // 替换原始内容
            recognitionResults.clear()
            recognitionResults.append(optimizedContent)
            tvResults.text = recognitionResults.toString()

            // 更新统计
            updateWordCount()

            showToast("已替换为优化后的内容")
            dialog.dismiss()
        }

        closeBtn.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun clearResults() {
        recognitionResults.clear()
        tvResults.text = ""
        tvPreview.text = ""
        cardPreview.visibility = View.GONE
        llActions.visibility = View.GONE
        updateWordCount()
        showToast("内容已清空")
    }
    
    private fun openSettings() {
        val intent = Intent(this, SettingsActivity::class.java)
        startActivity(intent)
    }
    
    private fun copyToClipboard(label: String, text: String) {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
        val clip = android.content.ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    private fun startAudioRecording() {
        Thread {
            try {
                val bufferSize = AudioRecord.getMinBufferSize(
                    SAMPLE_RATE,
                    android.media.AudioFormat.CHANNEL_IN_MONO,
                    android.media.AudioFormat.ENCODING_PCM_16BIT
                )

                audioRecord = AudioRecord(
                    android.media.MediaRecorder.AudioSource.MIC,
                    SAMPLE_RATE,
                    android.media.AudioFormat.CHANNEL_IN_MONO,
                    android.media.AudioFormat.ENCODING_PCM_16BIT,
                    bufferSize
                )

                audioRecord?.startRecording()

                val buffer = ShortArray(bufferSize)

                while (isRecording.get()) {
                    val ret = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                    if (ret > 0) {
                        val samples = FloatArray(ret) { buffer[it] / 32768.0f }
                        // Process audio through ASR engine
                        // Results are handled through the ASRListener callbacks
                        asrEngine.processAudio(samples)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Audio recording error", e)
                runOnUiThread {
                    showToast("录音错误: ${e.message}")
                    stopRecording()
                }
            }
        }.start()
    }

    private fun stopAudioRecording() {
        try {
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping audio recording", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isRecording.get()) {
            stopRecording()
        }
        stopAudioRecording()
        pulseAnimator?.cancel()
        waveformAnimator?.cancel()
    }
    
    enum class UIState {
        INITIALIZING, READY, RECORDING, PROCESSING, ERROR
    }
}
