<?xml version="1.0" encoding="utf-8"?>
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android">
    <aapt:attr name="android:drawable" xmlns:aapt="http://schemas.android.com/aapt">
        <vector android:width="200dp" android:height="200dp" android:viewportWidth="200" android:viewportHeight="200">
            <group android:name="pulse_group">
                <path android:name="pulse_ring"
                      android:pathData="M100,100 m-80,0 a80,80 0 1,1 160,0 a80,80 0 1,1 -160,0"
                      android:strokeColor="@color/recording_pulse"
                      android:strokeWidth="2"
                      android:strokeAlpha="0.6"
                      android:fillColor="@android:color/transparent"/>
            </group>
        </vector>
    </aapt:attr>
    
    <target android:name="pulse_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="1500"
                    android:valueFrom="0.8"
                    android:valueTo="1.2"
                    android:repeatCount="infinite"
                    android:repeatMode="reverse"
                    android:interpolator="@android:anim/accelerate_decelerate_interpolator"/>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="1500"
                    android:valueFrom="0.8"
                    android:valueTo="1.2"
                    android:repeatCount="infinite"
                    android:repeatMode="reverse"
                    android:interpolator="@android:anim/accelerate_decelerate_interpolator"/>
            </set>
        </aapt:attr>
    </target>
    
    <target android:name="pulse_ring">
        <aapt:attr name="android:animation">
            <objectAnimator
                android:propertyName="strokeAlpha"
                android:duration="1500"
                android:valueFrom="0.6"
                android:valueTo="0.1"
                android:repeatCount="infinite"
                android:repeatMode="reverse"
                android:interpolator="@android:anim/accelerate_decelerate_interpolator"/>
        </aapt:attr>
    </target>
</animated-vector>
